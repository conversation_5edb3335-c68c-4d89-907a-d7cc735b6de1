import './App.css'
import Counter from './components/Counter'

function App() {
    return (
        <div style={{
            padding: '20px',
            fontFamily: 'Arial, sans-serif',
            backgroundColor: '#f5f5f5',
            minHeight: '100vh'
        }}>
            <h1 style={{
                textAlign: 'center',
                marginBottom: '30px',
                color: '#333',
                fontSize: '2rem'
            }}>
                嵌套 Counter 组件演示
            </h1>

            <div style={{
                display: 'flex',
                justifyContent: 'center',
                alignItems: 'flex-start'
            }}>
                {/* 渲染一个 level 为 3 的顶层 Counter，会创建 4 层嵌套 */}
                <Counter level={3} />
            </div>

            <div style={{
                marginTop: '30px',
                padding: '20px',
                backgroundColor: 'white',
                borderRadius: '8px',
                boxShadow: '0 2px 4px rgba(0,0,0,0.1)'
            }}>
                <h2 style={{ color: '#333', marginBottom: '10px' }}>使用说明：</h2>
                <ul style={{ color: '#666', lineHeight: '1.6' }}>
                    <li>每个 Counter 都有独立的计数状态</li>
                    <li>子组件会显示父组件的 Count 值（只读，不能修改）</li>
                    <li>根节点（Level 3）没有父组件，所以不会显示父组件 Count</li>
                    <li>点击任意 Counter 的 "+1" 按钮，只会更新该 Counter 的计数</li>
                    <li>每次 Counter 重新渲染时，背景颜色会随机改变</li>
                    <li>Counter 支持嵌套，当前演示有 4 层嵌套（Level 3 到 Level 0）</li>
                    <li>观察：点击某个 Counter 时，只有它自己的背景色会改变，其他 Counter 不受影响</li>
                </ul>
            </div>
        </div>
    )
}

export default App
