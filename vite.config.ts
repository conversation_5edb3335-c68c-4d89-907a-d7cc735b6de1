import { defineConfig } from "vite";
import path from "path";
import react from "@vitejs/plugin-react-swc";
import babel from "vite-plugin-babel";
import tailwindcss from "@tailwindcss/vite";

// https://vite.dev/config/
export default defineConfig({
  plugins: [
    react(),
    babel({
      babelConfig: {
        plugins: ["babel-plugin-react-compiler"],
      },
    }),
    tailwindcss(),
  ],
  resolve: {
    alias: {
      "@": path.resolve(__dirname, "src"), // 将 @ 指向 src 目录
    },
  },
});
