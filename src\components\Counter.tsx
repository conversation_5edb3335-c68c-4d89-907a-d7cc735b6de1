import React, { useState } from 'react';

interface CounterProps {
  level: number;
}

// 生成随机颜色的函数
const getRandomColor = (): string => {
  const colors = [
    '#FFB6C1', // Light Pink
    '#87CEEB', // Sky Blue
    '#98FB98', // Pale Green
    '#F0E68C', // Khaki
    '#DDA0DD', // Plum
    '#F5DEB3', // Wheat
    '#FFE4E1', // Misty Rose
    '#E0FFFF', // Light Cyan
    '#FFEFD5', // Papaya Whip
    '#D3D3D3', // Light Gray
    '#FFA07A', // Light Salmon
    '#20B2AA', // Light Sea Green
    '#87CEFA', // Light Sky Blue
    '#FFFFE0', // Light Yellow
    '#FF69B4', // Hot Pink
    '#32CD32', // Lime Green
    '#FFD700', // Gold
    '#FF6347', // Tomato
    '#40E0D0', // Turquoise
    '#EE82EE', // Violet
  ];
  return colors[Math.floor(Math.random() * colors.length)];
};

const Counter: React.FC<CounterProps> = ({ level }) => {
  const [count, setCount] = useState(0);

  // 每次组件重新渲染时都会重新计算背景颜色
  // 这样可以准确反映组件的重新渲染状态
  const backgroundColor = getRandomColor();

  const handleIncrement = () => {
    setCount(prevCount => prevCount + 1);
  };

  return (
    <div
      style={{
        backgroundColor,
        border: '2px solid #333',
        borderRadius: '8px',
        padding: '16px',
        margin: '8px',
        minWidth: '200px',
        transition: 'background-color 0.3s ease',
      }}
    >
      <div style={{ marginBottom: '12px' }}>
        <h3 style={{ margin: '0 0 8px 0', color: '#333' }}>
          Counter (Level {level})
        </h3>
        <p style={{ margin: '0 0 8px 0', color: '#666' }}>
          Count: <strong>{count}</strong>
        </p>
        <button
          onClick={handleIncrement}
          style={{
            backgroundColor: '#007bff',
            color: 'white',
            border: 'none',
            borderRadius: '4px',
            padding: '8px 16px',
            cursor: 'pointer',
            fontSize: '14px',
            fontWeight: 'bold',
          }}
          onMouseOver={(e) => {
            e.currentTarget.style.backgroundColor = '#0056b3';
          }}
          onMouseOut={(e) => {
            e.currentTarget.style.backgroundColor = '#007bff';
          }}
        >
          +1
        </button>
      </div>

      {/* 当 level > 0 时，渲染子 Counter */}
      {level > 0 && (
        <div style={{ marginLeft: '16px', borderLeft: '2px dashed #999', paddingLeft: '16px' }}>
          <Counter level={level - 1} />
        </div>
      )}
    </div>
  );
};

export default Counter;
